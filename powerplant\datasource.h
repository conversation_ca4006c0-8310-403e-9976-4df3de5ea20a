#ifndef DATASOURCE_H
#define DATASOURCE_H

#include <QObject>
#include <QTimer>
#include <QVariantList>
#include <QDateTime>
#include <QThread>
#include <QMutex>
#include <QThreadPool>

QT_BEGIN_NAMESPACE
class QAbstractSeries;
QT_END_NAMESPACE

class DataSource : public QObject
{
    Q_OBJECT
    Q_PROPERTY(QVariantList smokeO2Data READ smokeO2Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeCOData READ smokeCOData NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeNOxData READ smokeNOxData NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeSO2Data READ smokeSO2Data NOTIFY smokeDataChanged)
    Q_PROPERTY(QVariantList smokeTableData READ smokeTableData NOTIFY smokeTableDataChanged)
    Q_PROPERTY(QStringList boilerList READ boilerList NOTIFY boilerListChanged)
    Q_PROPERTY(QString currentBoiler READ currentBoiler WRITE setCurrentBoiler NOTIFY currentBoilerChanged)
    Q_PROPERTY(bool isRunning READ isRunning WRITE setIsRunning NOTIFY isRunningChanged)
    Q_PROPERTY(bool isDataConnected READ isDataConnected NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString connectionStatus READ connectionStatus NOTIFY dataConnectionChanged)
    Q_PROPERTY(QString currentTemperature READ currentTemperature NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentVoltage READ currentVoltage NOTIFY currentDataChanged)
    Q_PROPERTY(QString currentCurrent READ currentCurrent NOTIFY currentDataChanged)

public:
    explicit DataSource(QObject *parent = nullptr);
    
    // 属性访问器
    QVariantList smokeO2Data() const { return m_smokeO2Data; }
    QVariantList smokeCOData() const { return m_smokeCOData; }
    QVariantList smokeNOxData() const { return m_smokeNOxData; }
    QVariantList smokeSO2Data() const { return m_smokeSO2Data; }
    QVariantList smokeTableData() const { return m_smokeTableData; }
    QStringList boilerList() const { return m_boilerList; }
    QString currentBoiler() const { return m_currentBoiler; }
    bool isRunning() const { return m_isRunning; }
    bool isDataConnected() const { return m_isDataConnected; }
    QString connectionStatus() const { return m_connectionStatus; }
    QString currentTemperature() const { return m_currentTemperature; }
    QString currentVoltage() const { return m_currentVoltage; }
    QString currentCurrent() const { return m_currentCurrent; }

    // 获取当前设备的采集间隔（秒）
    int getCurrentCollectionInterval() const;

    void setIsRunning(bool running);
    void setCurrentBoiler(const QString &boiler);

public slots:
    void startMonitoring();
    void stopMonitoring();
    void clearData();
    void updateSmokeChartSeries(QAbstractSeries *o2Series, QAbstractSeries *coSeries, QAbstractSeries *noxSeries, QAbstractSeries *so2Series, int zoomIndex = 0);
    void updateSmokeChartSeriesWithMinutes(QAbstractSeries *o2Series, QAbstractSeries *coSeries, QAbstractSeries *noxSeries, QAbstractSeries *so2Series);
    void updateSmokeChartSeriesWithScroll(QAbstractSeries *o2Series, QAbstractSeries *coSeries, QAbstractSeries *noxSeries, QAbstractSeries *so2Series, int zoomIndex, double scrollOffset);



signals:
    void smokeDataChanged();
    void smokeTableDataChanged();
    void boilerListChanged();
    void currentBoilerChanged();
    void isRunningChanged();
    void chartDataUpdated();
    void dataConnectionChanged();
    void currentDataChanged();
    // boilerSwitchCompleted信号已移除，使用简化的切换逻辑

private slots:
    void updateData();

private:
    void updateSmokeData();
    void loadBoilerList();
    void reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler);
    void addSmokeTableRow(double o2, double co, double nox, double so2, double temperature, double voltage, double current);
    void updateTimerInterval();

    // 异步锅炉切换相关方法已移除，使用简化的切换逻辑
    
    QTimer *m_timer;
    
    // 烟气数据
    QVariantList m_smokeO2Data;
    QVariantList m_smokeCOData;
    QVariantList m_smokeNOxData;
    QVariantList m_smokeSO2Data;

    // 表格数据
    QVariantList m_smokeTableData;

    // 烟气分析仪相关
    QStringList m_boilerList;  // 保持变量名以兼容现有代码
    QString m_currentBoiler;   // 保持变量名以兼容现有代码

    bool m_isRunning;
    bool m_isDataConnected;
    QString m_connectionStatus;
    int m_dataCount;
    
    // 当前数据值
    QString m_currentTemperature;
    QString m_currentVoltage;
    QString m_currentCurrent;

    // 相对时间轴相关变量
    QDateTime m_dataStartTime;  // 数据采集开始时间
    bool m_dataStartTimeSet;    // 是否已设置开始时间

    // 异步操作保护变量已移除，使用简化的切换逻辑

    static const int MAX_CHART_POINTS = 5760;  // 24小时 * 60分钟 * 4次/分钟 (15秒间隔)
    static const int MAX_TABLE_ROWS = 10;
};

#endif // DATASOURCE_H
