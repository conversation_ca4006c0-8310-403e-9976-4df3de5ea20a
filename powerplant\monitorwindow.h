#ifndef MONITORWINDOW_H
#define MONITORWINDOW_H

#include <QObject>
#include <QTimer>
#include "datasource.h"

class MonitorWindow : public QObject
{
    Q_OBJECT
    Q_PROPERTY(DataSource* dataSource READ dataSource CONSTANT)

public:
    explicit MonitorWindow(QObject *parent = nullptr);
    
    DataSource* dataSource() const { return m_dataSource; }

public slots:
    void startMonitoring();
    void stopMonitoring();
    void clearAllData();
    void switchBoilerAsync(const QString &boilerName);

private:
    DataSource *m_dataSource;
};

#endif // MONITORWINDOW_H
