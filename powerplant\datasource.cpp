#include "datasource.h"
#include <QVariantMap>
#include <QtCharts/QXYSeries>
#include <QtCharts/QLineSeries>
#include <QtCharts/QAbstractSeries>
#include <QDateTime>
#include <QTimer>
#include <QMutexLocker>
#include <QThreadPool>
#include <QRunnable>
#include <QMetaObject>
#include <thread>
#include <chrono>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// BoilerSwitchTask类已移除，因为使用简化的锅炉切换逻辑

// 条件编译：启用硬件数据采集
#define ENABLE_HARDWARE_DATA
#ifdef ENABLE_HARDWARE_DATA
#include "data.h"
#include "boiler.h"
#include "config_manager.h"
#endif

DataSource::DataSource(QObject *parent)
    : QObject(parent)
    , m_timer(new QTimer(this))
    , m_isRunning(false)
    , m_isDataConnected(false)
    , m_connectionStatus("未连接串口数据采集设备")
    , m_dataCount(0)
    , m_dataStartTimeSet(false)
    , m_currentTemperature("0.0℃")
    , m_currentVoltage("0.0V")
    , m_currentCurrent("0.000A")
{
    connect(m_timer, &QTimer::timeout, this, &DataSource::updateData);
    // 不设置初始间隔，等待从配置文件读取

    // 加载锅炉列表
    loadBoilerList();

    // 设置初始定时器间隔（从配置文件读取）
    updateTimerInterval();
}

void DataSource::setIsRunning(bool running)
{
    if (m_isRunning != running) {
        m_isRunning = running;
        emit isRunningChanged();

        if (running) {
            startMonitoring();
        } else {
            stopMonitoring();
        }
    }
}

void DataSource::setCurrentBoiler(const QString &boiler)
{
    if (m_currentBoiler != boiler) {
        debug_printf("切换烟气分析仪: 从 '%s' 到 '%s'\n",
                    m_currentBoiler.toStdString().c_str(), boiler.toStdString().c_str());

        // 简化的烟气分析仪切换逻辑，因为每个设备都有独立线程在采集数据
        m_currentBoiler = boiler;

        // 更新定时器间隔以匹配新锅炉的采集间隔
        updateTimerInterval();

        // 立即发射信号，让UI知道设备已经改变
        emit currentBoilerChanged();

        // 清空历史数据，准备显示新设备的数据
        m_smokeO2Data.clear();
        m_smokeCOData.clear();
        m_smokeNOxData.clear();
        m_smokeSO2Data.clear();
        m_smokeTableData.clear();
        m_dataCount = 0;

        // 重置相对时间轴的开始时间
        m_dataStartTimeSet = false;

        // 重置连接状态，让下次updateData时重新检测
        m_isDataConnected = false;
        m_connectionStatus = "正在检测新烟气分析仪的串口连接...";
        emit dataConnectionChanged();

        // 发射数据变化信号
        emit smokeDataChanged();
        emit smokeTableDataChanged();
        emit chartDataUpdated();

        // 如果监控正在运行，立即检测新设备的连接状态
        if (m_isRunning) {
            debug_printf("设备切换: 立即检测新设备 '%s' 的连接状态\n", boiler.toStdString().c_str());
            updateData();
        }

        debug_printf("烟气分析仪切换完成: '%s'\n", boiler.toStdString().c_str());
    }
}

void DataSource::startMonitoring()
{
    if (!m_timer->isActive()) {
        m_timer->start();
        m_isRunning = true;
        emit isRunningChanged();

        // 初始化连接状态检查
        m_isDataConnected = false;
        m_connectionStatus = "正在检测串口数据采集设备...";
        emit dataConnectionChanged();

        // 立即进行一次数据更新和连接状态检测，不等待定时器
        debug_printf("UI监控启动: 立即进行首次连接状态检测\n");
        updateData();

        // 基于配置的动态延迟更新数据
        int collectionInterval = getCurrentCollectionInterval();
        int firstDelay = collectionInterval * 1000;      // 第一次延迟：1个采集周期
        int secondDelay = collectionInterval * 1500;     // 第二次延迟：1.5个采集周期

        debug_printf("UI监控: 使用动态延迟 - 第一次=%d毫秒, 第二次=%d毫秒\n",
                    firstDelay, secondDelay);

        QTimer::singleShot(firstDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%d秒后再次更新数据\n", collectionInterval);
            updateData();
            emit smokeDataChanged();  // 强制触发数据变化信号
            emit chartDataUpdated();  // 强制触发图表更新信号
        });

        QTimer::singleShot(secondDelay, this, [this, collectionInterval]() {
            debug_printf("UI监控: 延迟%.1f秒后第三次更新数据\n", collectionInterval * 1.5);
            updateData();
            emit smokeDataChanged();  // 强制触发数据变化信号
            emit chartDataUpdated();  // 强制触发图表更新信号
        });
    }
}

void DataSource::stopMonitoring()
{
    if (m_timer->isActive()) {
        m_timer->stop();
        m_isRunning = false;
        emit isRunningChanged();

        // 停止监控时重置连接状态
        m_isDataConnected = false;
        m_connectionStatus = "数据监控已停止";
        emit dataConnectionChanged();
    }
}

int DataSource::getCurrentCollectionInterval() const
{
    // 获取当前锅炉的采集间隔
    int interval = 15;  // 默认值

    if (!m_currentBoiler.isEmpty()) {
        extern ConfigManager* g_config_manager;
        if (g_config_manager) {
            interval = g_config_manager->get<int>(m_currentBoiler.toStdString(), "CollectionInterval", 15);
        }
    }

    debug_printf("UI监控采集间隔: 锅炉='%s', 间隔=%d秒\n",
                m_currentBoiler.toStdString().c_str(), interval);
    return interval;
}

void DataSource::clearData()
{
    m_smokeO2Data.clear();
    m_smokeCOData.clear();
    m_smokeNOxData.clear();
    m_smokeSO2Data.clear();
    m_smokeTableData.clear();
    m_dataCount = 0;

    // 重置相对时间轴的开始时间
    m_dataStartTimeSet = false;

    emit smokeDataChanged();
    emit smokeTableDataChanged();
    emit chartDataUpdated();
}

void DataSource::updateData()
{
    static QDateTime lastUpdateTime = QDateTime::currentDateTime();
    QDateTime currentTime = QDateTime::currentDateTime();
    qint64 timeDiff = lastUpdateTime.msecsTo(currentTime);

    debug_printf("UI数据更新调用 - 距离上次更新: %lld毫秒, 定时器间隔: %d毫秒\n",
                timeDiff, m_timer->interval());

    updateSmokeData();
    m_dataCount++;

    lastUpdateTime = currentTime;
}



void DataSource::updateSmokeData()
{
    // 检查是否有选择的烟气分析仪
    if (m_currentBoiler.isEmpty()) {
        return;
    }

    // 从硬件获取真实烟气数据
    float o2 = 0.0f, co = 0.0f, nox = 0.0f, so2 = 0.0f;
    float current = 0.0f, voltage = 0.0f, temperature = 0.0f;
    bool hardwareConnected = false;

    // 检查硬件连接状态
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    std::string deviceName = m_currentBoiler.toStdString();

    debug_printf("连接状态检测: 设备名='%s', boiler_map大小=%zu\n", deviceName.c_str(), boiler_map.size());

    auto it = boiler_map.find(deviceName);
    if (it != boiler_map.end()) {
        if (it->second != nullptr) {
            debug_printf("连接状态检测: 找到设备对象，fd=%d, is_initialized=%s\n",
                        it->second->fd, it->second->is_initialized ? "true" : "false");

            if (it->second->fd >= 0) {
                hardwareConnected = true;
                debug_printf("连接状态检测: ✓ 硬件连接正常\n");

                // 只有在硬件连接时才获取数据
                try {
                    get_realtime_data(deviceName, &co, &o2, &nox, &so2, &current, &voltage, &temperature);
                    debug_printf("烟气数据获取: %s - CO=%.2f, O2=%.2f, NOx=%.2f, SO2=%.2f\n",
                                deviceName.c_str(), co, o2, nox, so2);
                } catch (...) {
                    // 如果数据采集函数出现异常，设置为无效数据
                    debug_printf("烟气数据获取异常: %s\n", deviceName.c_str());
                    o2 = co = nox = so2 = current = voltage = temperature = 0.0f;
                    hardwareConnected = false;
                }
            } else {
                debug_printf("连接状态检测: ✗ 串口文件描述符无效: %d\n", it->second->fd);
            }
        } else {
            debug_printf("连接状态检测: ✗ 设备对象为空指针\n");
        }
    } else {
        debug_printf("连接状态检测: ✗ 在boiler_map中找不到设备 '%s'\n", deviceName.c_str());
    }
#endif

    // 更新连接状态
    if (hardwareConnected) {
        // 直接使用仪器返回的数据，不做有效性检查
        debug_printf("烟气数据直接使用: %s - O2=%.2f, CO=%.2f, NOx=%.2f, SO2=%.2f\n",
                    deviceName.c_str(), o2, co, nox, so2);

        // 硬件连接时直接处理数据
        // 更新连接状态
        if (!m_isDataConnected) {
            m_isDataConnected = true;
            m_connectionStatus = "烟气分析仪已连接";
            debug_printf("UI连接状态更新: ✓ 设置为已连接状态\n");
            emit dataConnectionChanged();
        } else {
            debug_printf("UI连接状态: 保持已连接状态\n");
        }



        // 获取当前时间
        QDateTime now = QDateTime::currentDateTime();

        // 如果是第一次采集数据，设置开始时间
        if (!m_dataStartTimeSet) {
            m_dataStartTime = now;
            m_dataStartTimeSet = true;
            debug_printf("设置数据采集开始时间: %s\n", m_dataStartTime.toString().toStdString().c_str());
        }

        // 获取当前锅炉的采集间隔配置
        int collectionIntervalSeconds = 15; // 默认15秒
#ifdef ENABLE_HARDWARE_DATA
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        extern ConfigManager* g_config_manager;

        std::string boilerName = m_currentBoiler.toStdString();
        auto it = boiler_map.find(boilerName);
        if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
            collectionIntervalSeconds = it->second->collection_interval;
        } else if (g_config_manager && g_config_manager->exists(boilerName, "CollectionInterval")) {
            collectionIntervalSeconds = g_config_manager->get<int>(boilerName, "CollectionInterval", 15);
        }
#endif

        // 计算基于配置采集间隔的相对时间，而不是基于实际时间流逝
        // 这样确保图表上的数据点位置严格按照配置的采集间隔排列
        double relativeTimeHours = (m_dataCount * collectionIntervalSeconds) / (60.0 * 60.0);  // 转换为小时
        double relativeTimeMinutes = (m_dataCount * collectionIntervalSeconds) / 60.0;  // 转换为分钟

        debug_printf("数据点 %d: 配置采集间隔=%d秒, 计算时间位置=%.3f小时(%.1f分钟)\n",
                    m_dataCount, collectionIntervalSeconds, relativeTimeHours, relativeTimeMinutes);

        // 更新图表数据 - 使用相对时间作为X坐标
        QVariantMap o2Point;
        o2Point["x"] = relativeTimeHours;  // 默认使用小时
        o2Point["x_minutes"] = relativeTimeMinutes;  // 保存分钟数据供1小时视图使用
        o2Point["y"] = o2;
        m_smokeO2Data.append(o2Point);

        QVariantMap coPoint;
        coPoint["x"] = relativeTimeHours;
        coPoint["x_minutes"] = relativeTimeMinutes;
        coPoint["y"] = co;
        m_smokeCOData.append(coPoint);

        QVariantMap noxPoint;
        noxPoint["x"] = relativeTimeHours;
        noxPoint["x_minutes"] = relativeTimeMinutes;
        noxPoint["y"] = nox;
        m_smokeNOxData.append(noxPoint);

        QVariantMap so2Point;
        so2Point["x"] = relativeTimeHours;
        so2Point["x_minutes"] = relativeTimeMinutes;
        so2Point["y"] = so2;
        m_smokeSO2Data.append(so2Point);

        // 保持最多24小时的数据点，移除超过24小时的旧数据
        while (!m_smokeO2Data.isEmpty()) {
            QVariantMap firstPoint = m_smokeO2Data.first().toMap();
            double firstPointTime = firstPoint["x"].toDouble();
            if (firstPointTime < (relativeTimeHours - 24.0)) {  // 超过24小时的数据
                m_smokeO2Data.removeFirst();
                m_smokeCOData.removeFirst();
                m_smokeNOxData.removeFirst();
                m_smokeSO2Data.removeFirst();
            } else {
                break;
            }
        }

        // 更新当前数据值
        m_currentTemperature = QString::number(temperature, 'f', 1) + "℃";
        m_currentVoltage = QString::number(voltage, 'f', 1) + "V";
        m_currentCurrent = QString::number(current, 'f', 3) + "A";

        // 添加表格数据
        addSmokeTableRow(o2, co, nox, so2, temperature, voltage, current);
    } else {
        // 硬件未连接
        debug_printf("UI连接状态检测: ✗ 硬件未连接\n");
        if (m_isDataConnected) {
            m_isDataConnected = false;
            m_connectionStatus = "烟气分析仪未连接";
            debug_printf("UI连接状态更新: ✗ 设置为未连接状态\n");
            emit dataConnectionChanged();
        } else {
            debug_printf("UI连接状态: 保持未连接状态\n");
        }

        // 硬件未连接时设置默认值
        m_currentTemperature = "0.0℃";
        m_currentVoltage = "0.0V";
        m_currentCurrent = "0.000A";
        // 不添加任何数据到图表和表格
    }

    emit smokeDataChanged();
    emit chartDataUpdated();
    emit currentDataChanged();
}

void DataSource::loadBoilerList()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    m_boilerList.clear();

    // 从全局设备映射中获取烟气分析仪列表
    for (const auto& pair : boiler_map) {
        m_boilerList.append(QString::fromStdString(pair.first));
    }

    // 如果有烟气分析仪，设置第一个为默认选择
    if (!m_boilerList.isEmpty() && m_currentBoiler.isEmpty()) {
        m_currentBoiler = m_boilerList.first();
        debug_printf("设置默认烟气分析仪: '%s'\n", m_currentBoiler.toStdString().c_str());

        // 如果监控正在运行，立即检测默认设备的连接状态
        if (m_isRunning) {
            debug_printf("默认设备设置: 立即检测连接状态\n");
            updateData();
        }
    }

    emit boilerListChanged();
    emit currentBoilerChanged();
#else
    // 如果硬件数据采集被禁用，提供默认烟气分析仪列表
    m_boilerList << "SmokeAnalyzer1" << "SmokeAnalyzer2";
    if (m_currentBoiler.isEmpty()) {
        m_currentBoiler = "SmokeAnalyzer1";
    }
    emit boilerListChanged();
    emit currentBoilerChanged();
#endif
}



void DataSource::addSmokeTableRow(double o2, double co, double nox, double so2, double temperature, double voltage, double current)
{
    QVariantMap row;
    row["time"] = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    row["o2"] = QString::number(o2, 'f', 3);
    row["co"] = QString::number(co, 'f', 0);
    row["nox"] = QString::number(nox, 'f', 1);
    row["so2"] = QString::number(so2, 'f', 0);
    row["temperature"] = QString::number(temperature, 'f', 1);
    row["voltage"] = QString::number(voltage, 'f', 1);
    row["current"] = QString::number(current, 'f', 3);

    m_smokeTableData.prepend(row);

    // 保持最多10行数据
    if (m_smokeTableData.size() > MAX_TABLE_ROWS) {
        m_smokeTableData.removeLast();
    }

    emit smokeTableDataChanged();
}



void DataSource::updateSmokeChartSeries(QAbstractSeries *o2Series, QAbstractSeries *coSeries, QAbstractSeries *noxSeries, QAbstractSeries *so2Series, int zoomIndex)
{
    if (!o2Series || !coSeries || !noxSeries || !so2Series) {
        return;
    }

    QLineSeries *o2LineSeries = qobject_cast<QLineSeries*>(o2Series);
    QLineSeries *coLineSeries = qobject_cast<QLineSeries*>(coSeries);
    QLineSeries *noxLineSeries = qobject_cast<QLineSeries*>(noxSeries);
    QLineSeries *so2LineSeries = qobject_cast<QLineSeries*>(so2Series);

    if (!o2LineSeries || !coLineSeries || !noxLineSeries || !so2LineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();
    noxLineSeries->clear();
    so2LineSeries->clear();

    // 根据缩放级别确定显示的时间范围
    double displayRangeHours = 24.0;  // 默认24小时
    switch(zoomIndex) {
        case 0: displayRangeHours = 24.0; break;  // 24小时
        case 1: displayRangeHours = 12.0; break;  // 12小时
        case 2: displayRangeHours = 8.0; break;   // 8小时
        case 3: displayRangeHours = 1.0; break;   // 1小时（这里用小时计算，后面会转换）
        default: displayRangeHours = 24.0; break;
    }

    // 计算时间偏移量：当数据超过显示范围时，让曲线向左移动
    double timeOffset = 0.0;
    if (!m_smokeO2Data.isEmpty()) {
        QVariantMap lastPoint = m_smokeO2Data.last().toMap();
        double currentTime = lastPoint["x"].toReal();

        // 如果当前时间超过显示范围，计算偏移量让最新数据显示在范围末尾
        if (currentTime > displayRangeHours) {
            timeOffset = currentTime - displayRangeHours;
        }
    }

    // 添加O2数据（X轴固定，曲线根据timeOffset移动）
    for (int i = 0; i < m_smokeO2Data.size(); ++i) {
        QVariantMap point = m_smokeO2Data[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;  // 减去偏移量
        qreal value = point["y"].toReal();

        // 只显示在显示范围内的数据点
        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            o2LineSeries->append(displayTime, value);
        }
    }

    // 添加CO数据
    for (int i = 0; i < m_smokeCOData.size(); ++i) {
        QVariantMap point = m_smokeCOData[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            coLineSeries->append(displayTime, value);
        }
    }

    // 添加NOx数据
    for (int i = 0; i < m_smokeNOxData.size(); ++i) {
        QVariantMap point = m_smokeNOxData[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            noxLineSeries->append(displayTime, value);
        }
    }

    // 添加SO2数据
    for (int i = 0; i < m_smokeSO2Data.size(); ++i) {
        QVariantMap point = m_smokeSO2Data[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            so2LineSeries->append(displayTime, value);
        }
    }
}

void DataSource::updateSmokeChartSeriesWithMinutes(QAbstractSeries *o2Series, QAbstractSeries *coSeries, QAbstractSeries *noxSeries, QAbstractSeries *so2Series)
{
    if (!o2Series || !coSeries || !noxSeries || !so2Series) {
        return;
    }

    QLineSeries *o2LineSeries = qobject_cast<QLineSeries*>(o2Series);
    QLineSeries *coLineSeries = qobject_cast<QLineSeries*>(coSeries);
    QLineSeries *noxLineSeries = qobject_cast<QLineSeries*>(noxSeries);
    QLineSeries *so2LineSeries = qobject_cast<QLineSeries*>(so2Series);

    if (!o2LineSeries || !coLineSeries || !noxLineSeries || !so2LineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();
    noxLineSeries->clear();
    so2LineSeries->clear();

    // 计算分钟时间偏移量：当数据超过60分钟时，让曲线向左移动
    double timeOffsetMinutes = 0.0;
    if (!m_smokeO2Data.isEmpty()) {
        QVariantMap lastPoint = m_smokeO2Data.last().toMap();
        double currentTimeMinutes = lastPoint["x_minutes"].toReal();

        // 如果当前时间超过60分钟，计算偏移量让最新数据显示在60分钟位置
        if (currentTimeMinutes > 60.0) {
            timeOffsetMinutes = currentTimeMinutes - 60.0;
        }
    }

    // 添加O2数据（X轴固定0-60分钟，曲线根据timeOffsetMinutes移动）
    for (int i = 0; i < m_smokeO2Data.size(); ++i) {
        QVariantMap point = m_smokeO2Data[i].toMap();
        qreal absoluteTimeMinutes = point["x_minutes"].toReal();
        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;
        qreal value = point["y"].toReal();

        // 只显示在0-60范围内的数据点
        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            o2LineSeries->append(displayTimeMinutes, value);
        }
    }

    // 添加CO数据
    for (int i = 0; i < m_smokeCOData.size(); ++i) {
        QVariantMap point = m_smokeCOData[i].toMap();
        qreal absoluteTimeMinutes = point["x_minutes"].toReal();
        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;
        qreal value = point["y"].toReal();

        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            coLineSeries->append(displayTimeMinutes, value);
        }
    }

    // 添加NOx数据
    for (int i = 0; i < m_smokeNOxData.size(); ++i) {
        QVariantMap point = m_smokeNOxData[i].toMap();
        qreal absoluteTimeMinutes = point["x_minutes"].toReal();
        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;
        qreal value = point["y"].toReal();

        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            noxLineSeries->append(displayTimeMinutes, value);
        }
    }

    // 添加SO2数据
    for (int i = 0; i < m_smokeSO2Data.size(); ++i) {
        QVariantMap point = m_smokeSO2Data[i].toMap();
        qreal absoluteTimeMinutes = point["x_minutes"].toReal();
        qreal displayTimeMinutes = absoluteTimeMinutes - timeOffsetMinutes;
        qreal value = point["y"].toReal();

        if (displayTimeMinutes >= 0.0 && displayTimeMinutes <= 60.0) {
            so2LineSeries->append(displayTimeMinutes, value);
        }
    }
}

void DataSource::updateSmokeChartSeriesWithScroll(QAbstractSeries *o2Series, QAbstractSeries *coSeries, QAbstractSeries *noxSeries, QAbstractSeries *so2Series, int zoomIndex, double scrollOffset)
{
    if (!o2Series || !coSeries || !noxSeries || !so2Series) {
        return;
    }

    QLineSeries *o2LineSeries = qobject_cast<QLineSeries*>(o2Series);
    QLineSeries *coLineSeries = qobject_cast<QLineSeries*>(coSeries);
    QLineSeries *noxLineSeries = qobject_cast<QLineSeries*>(noxSeries);
    QLineSeries *so2LineSeries = qobject_cast<QLineSeries*>(so2Series);

    if (!o2LineSeries || !coLineSeries || !noxLineSeries || !so2LineSeries) {
        return;
    }

    // 清空现有数据
    o2LineSeries->clear();
    coLineSeries->clear();
    noxLineSeries->clear();
    so2LineSeries->clear();

    // 根据缩放级别确定显示的时间范围
    double displayRangeHours = 24.0;  // 默认24小时
    switch(zoomIndex) {
        case 0: displayRangeHours = 24.0; break;  // 24小时
        case 1: displayRangeHours = 12.0; break;  // 12小时
        case 2: displayRangeHours = 8.0; break;   // 8小时
        case 3: displayRangeHours = 1.0; break;   // 1小时
        default: displayRangeHours = 24.0; break;
    }

    // 使用手动滚动偏移量而不是自动计算
    double timeOffset = scrollOffset;

    // 添加O2数据（X轴固定，曲线根据timeOffset移动）
    for (int i = 0; i < m_smokeO2Data.size(); ++i) {
        QVariantMap point = m_smokeO2Data[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;  // 减去偏移量
        qreal value = point["y"].toReal();

        // 只显示在显示范围内的数据点
        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            o2LineSeries->append(displayTime, value);
        }
    }

    // 添加CO数据
    for (int i = 0; i < m_smokeCOData.size(); ++i) {
        QVariantMap point = m_smokeCOData[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            coLineSeries->append(displayTime, value);
        }
    }

    // 添加NOx数据
    for (int i = 0; i < m_smokeNOxData.size(); ++i) {
        QVariantMap point = m_smokeNOxData[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            noxLineSeries->append(displayTime, value);
        }
    }

    // 添加SO2数据
    for (int i = 0; i < m_smokeSO2Data.size(); ++i) {
        QVariantMap point = m_smokeSO2Data[i].toMap();
        qreal absoluteTime = point["x"].toReal();
        qreal displayTime = absoluteTime - timeOffset;
        qreal value = point["y"].toReal();

        if (displayTime >= 0.0 && displayTime <= displayRangeHours) {
            so2LineSeries->append(displayTime, value);
        }
    }
}

void DataSource::reinitializeSerialConnection(const QString &oldBoiler, const QString &newBoiler)
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;

    if (newBoiler.isEmpty()) {
        debug_printf("新烟气分析仪名称为空，跳过串口重新初始化\n");
        return;
    }

    std::string newDeviceName = newBoiler.toStdString();
    debug_printf("开始为烟气分析仪 '%s' 重新初始化串口连接\n", newDeviceName.c_str());

    // 查找新烟气分析仪
    auto newIt = boiler_map.find(newDeviceName);
    if (newIt == boiler_map.end() || newIt->second == nullptr) {
        debug_printf("错误: 找不到烟气分析仪 '%s'，设备映射大小: %zu\n", newDeviceName.c_str(), boiler_map.size());
        return;
    }

    Boiler* newDeviceObj = newIt->second;
    debug_printf("找到烟气分析仪 '%s'，当前fd: %d，协议: '%s'\n",
                newDeviceName.c_str(), newDeviceObj->fd, newDeviceObj->protocol.c_str());

    // 检查新烟气分析仪的串口连接状态
    if (newDeviceObj->fd < 0) {
        // 串口未连接，需要重新初始化
        debug_printf("烟气分析仪 '%s' 串口未连接，开始重新初始化串口连接\n", newDeviceName.c_str());

        // 获取配置管理器
        extern ConfigManager* g_config_manager;
        if (g_config_manager == nullptr) {
            debug_printf("错误: 全局配置管理器未初始化\n");
            return;
        }

        try {
            // 重新获取协议配置并初始化串口
            std::string protocol = newDeviceObj->protocol;
            if (protocol.empty()) {
                // 如果协议为空，从配置文件重新读取
                protocol = g_config_manager->get<std::string>(newDeviceName, "Protocol");
                newDeviceObj->protocol = protocol;
                debug_printf("从配置文件重新读取烟气分析仪 '%s' 的协议: '%s'\n", newDeviceName.c_str(), protocol.c_str());
            }

            // 获取协议对应的串口配置
            std::string port = g_config_manager->get<std::string>(protocol, "Port");
            int baud_rate = g_config_manager->get<int>(protocol, "BaudRate", 9600);
            char parity = g_config_manager->get<char>(protocol, "Parity");
            int stop_bits = g_config_manager->get<int>(protocol, "StopBits", 1);
            int data_bits = g_config_manager->get<int>(protocol, "DataBits", 8);

            debug_printf("重新初始化烟气分析仪 '%s' 串口配置:\n", newDeviceName.c_str());
            debug_printf("  协议: '%s'\n", protocol.c_str());
            debug_printf("  端口: '%s'\n", port.c_str());
            debug_printf("  波特率: %d\n", baud_rate);
            debug_printf("  校验位: %c\n", parity);
            debug_printf("  停止位: %d\n", stop_bits);
            debug_printf("  数据位: %d\n", data_bits);

            // 重新打开串口
            extern int open_serial_port(const char *device, int speed, char parity, int stop_bits, int data_bits);
            int new_fd = open_serial_port(port.c_str(), baud_rate, parity, stop_bits, data_bits);

            if (new_fd >= 0) {
                newDeviceObj->fd = new_fd;
                debug_printf("烟气分析仪 '%s' 串口重新初始化成功，文件描述符: %d\n", newDeviceName.c_str(), new_fd);
            } else {
                debug_printf("烟气分析仪 '%s' 串口重新初始化失败\n", newDeviceName.c_str());
            }
        } catch (const std::exception& e) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生异常: %s\n", newDeviceName.c_str(), e.what());
        } catch (...) {
            debug_printf("重新初始化烟气分析仪 '%s' 串口时发生未知异常\n", newDeviceName.c_str());
        }
    } else {
        debug_printf("烟气分析仪 '%s' 串口已连接，文件描述符: %d\n", newDeviceName.c_str(), newDeviceObj->fd);
    }

    // 输出切换完成信息
    if (!oldBoiler.isEmpty()) {
        debug_printf("烟气分析仪切换完成: 从 '%s' 切换到 '%s'\n", oldBoiler.toStdString().c_str(), newDeviceName.c_str());
    } else {
        debug_printf("初始化烟气分析仪 '%s' 完成\n", newDeviceName.c_str());
    }

#else
    debug_printf("硬件数据采集被禁用，跳过串口重新初始化\n");
#endif
}

// performBoilerSwitchInBackground方法已移除，使用简化的切换逻辑

// performBoilerSwitchAsync方法已移除，使用简化的切换逻辑

// checkBoilerConnection方法已移除，使用简化的切换逻辑

// closeBoilerConnection方法已移除，因为每个锅炉的线程独立管理串口连接

void DataSource::updateTimerInterval()
{
#ifdef ENABLE_HARDWARE_DATA
    extern std::unordered_map<std::string, Boiler*> boiler_map;
    extern ConfigManager* g_config_manager;

    if (g_config_manager == nullptr) {
        debug_printf("⚠ 配置管理器未初始化，无法设置UI更新间隔\n");
        return;
    }

    // 如果没有选择锅炉，尝试从第一个可用锅炉获取配置
    std::string boilerName;
    if (m_currentBoiler.isEmpty()) {
        if (!boiler_map.empty()) {
            boilerName = boiler_map.begin()->first;
            debug_printf("调试: 没有选择锅炉，使用第一个可用锅炉 '%s' 的配置\n", boilerName.c_str());
        } else {
            debug_printf("⚠ 没有可用的锅炉配置\n");
            return;
        }
    } else {
        boilerName = m_currentBoiler.toStdString();
    }

    debug_printf("调试: 正在为锅炉 '%s' 更新UI定时器间隔\n", boilerName.c_str());

    // 首先尝试从锅炉对象获取采集间隔
    auto it = boiler_map.find(boilerName);
    if (it != boiler_map.end() && it->second != nullptr && it->second->is_initialized) {
        int collectionInterval = it->second->collection_interval;
        int uiInterval = collectionInterval * 1000; // 转换为毫秒
        m_timer->setInterval(uiInterval);
        debug_printf("✓ 从锅炉对象获取采集间隔: %d秒，设置UI更新间隔: %d毫秒\n", collectionInterval, uiInterval);
        debug_printf("✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
        return;
    }

    // 如果锅炉对象不可用，直接从配置文件读取
    if (g_config_manager->exists(boilerName, "CollectionInterval")) {
        int collectionInterval = g_config_manager->get<int>(boilerName, "CollectionInterval");
        if (collectionInterval > 0) {
            int uiInterval = collectionInterval * 1000; // 转换为毫秒
            m_timer->setInterval(uiInterval);
            debug_printf("✓ 从配置文件获取锅炉 '%s' 采集间隔: %d秒，设置UI更新间隔: %d毫秒\n",
                        boilerName.c_str(), collectionInterval, uiInterval);
            debug_printf("✓ 当前定时器实际间隔: %d毫秒\n", m_timer->interval());
            return;
        } else {
            debug_printf("⚠ 锅炉 '%s' 的采集间隔配置无效: %d秒\n", boilerName.c_str(), collectionInterval);
        }
    } else {
        debug_printf("⚠ 锅炉 '%s' 的配置文件中缺少 CollectionInterval 字段\n", boilerName.c_str());
    }
#endif

    debug_printf("⚠ 无法获取有效的采集间隔配置，定时器未设置\n");
}
