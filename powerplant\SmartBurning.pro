QT += core widgets quick quickcontrols2 charts multimedia multimediawidgets network

# 根据构建类型决定是否显示控制台
CONFIG += c++17
# 静态链接选项（用于更好的兼容性）
CONFIG += static
CONFIG(debug, debug|release) {
    CONFIG += console debug
    # 启用硬件数据采集和调试输出
    DEFINES += ENABLE_HARDWARE_DATA
    DEFINES += DEBUG
} else {
    # Release模式下不显示控制台，禁用调试输出
    DEFINES += ENABLE_HARDWARE_DATA
    DEFINES += QT_NO_DEBUG_OUTPUT
}

# Windows specific libraries
win32 {
    LIBS += -lws2_32
    # 设置Windows目标版本为Windows 7兼容
    DEFINES += WINVER=0x0601 _WIN32_WINNT=0x0601
    # 禁用新的Windows API
    DEFINES += WIN32_LEAN_AND_MEAN
    # 强制使用旧版本API
    QMAKE_CXXFLAGS += -D_WIN32_WINNT=0x0601
    QMAKE_CFLAGS += -D_WIN32_WINNT=0x0601
    # 链接器设置，确保兼容性
    QMAKE_LFLAGS += -Wl,--major-subsystem-version,6 -Wl,--minor-subsystem-version,1
    # 禁用Qt中可能使用的新Windows API
    DEFINES += QT_NO_WIN_ACTIVEQT
    # 强制使用兼容模式
    QMAKE_CXXFLAGS += -DWINVER=0x0601 -D_WIN32_WINNT=0x0601
}

# You can make your code fail to compile if it uses deprecated APIs.
# In order to do so, uncomment the following line.
#DEFINES += QT_DISABLE_DEPRECATED_BEFORE=0x060000    # disables all the APIs deprecated before Qt 6.0.0

TARGET = SmartBurning
TEMPLATE = app

# Source files
SOURCES += \
    main.cpp \
    widget.cpp \
    modelmanager.cpp \
    monitorwindow.cpp \
    datasource.cpp \
    datascreen.cpp \
    videoplayer.cpp \
    videomanager.cpp \
    boiler.cpp \
    config_manager.cpp \
    configmanager_qml.cpp \
    data.cpp \
    csvfile.cpp \
    csvreader.cpp \
    dcs.cpp

# Header files
HEADERS += \
    widget.h \
    modelmanager.h \
    monitorwindow.h \
    datasource.h \
    datascreen.h \
    videoplayer.h \
    videomanager.h \
    boiler.h \
    config_manager.h \
    configmanager_qml.h \
    data.h \
    csvfile.h \
    csvreader.h \
    dcs.h

# UI files
FORMS += \
    widget.ui \
    videoplayer.ui

# Resources
RESOURCES += qml.qrc

# Translation files
TRANSLATIONS += SmartBurning_zh_CN.ts

# Other files
OTHER_FILES += \
    config.ini \
    Windows串口使用说明.md \
    数据采集集成说明.md

# Copy video folder and config files to output directory
win32 {
    CONFIG(debug, debug|release) {
        DESTDIR = $$OUT_PWD/debug
    } else {
        DESTDIR = $$OUT_PWD/release
    }

    # Copy video folder
    video.path = $$DESTDIR/video
    video.files = $$PWD/video/*
    INSTALLS += video

    # Copy data folder (create empty directory if needed)
    data.path = $$DESTDIR/data
    data.files = $$PWD/data/*
    INSTALLS += data

    # Copy config file
    config.path = $$DESTDIR
    config.files = $$PWD/config.ini
    INSTALLS += config

    # Create necessary directories and copy files
    QMAKE_POST_LINK += $$quote(if not exist \"$$shell_path($$DESTDIR)\" mkdir \"$$shell_path($$DESTDIR)\")
    QMAKE_POST_LINK += $$quote(&& if not exist \"$$shell_path($$DESTDIR/data)\" mkdir \"$$shell_path($$DESTDIR/data)\")
    QMAKE_POST_LINK += $$quote(&& if not exist \"$$shell_path($$DESTDIR/video)\" mkdir \"$$shell_path($$DESTDIR/video)\")

    # Copy config file (essential for startup)
    QMAKE_POST_LINK += $$quote(&& copy /Y \"$$shell_path($$PWD/config.ini)\" \"$$shell_path($$DESTDIR/)\")

    # Copy video files if they exist
    QMAKE_POST_LINK += $$quote(&& if exist \"$$shell_path($$PWD/video)\" xcopy /E /I /Y \"$$shell_path($$PWD/video)\" \"$$shell_path($$DESTDIR/video)\")
}

unix {
    # Copy video folder and config files for Unix systems
    video.path = $$OUT_PWD/video
    video.files = $$PWD/video/*
    INSTALLS += video

    # Copy data folder
    data.path = $$OUT_PWD/data
    data.files = $$PWD/data/*
    INSTALLS += data

    # Copy config file
    config.path = $$OUT_PWD
    config.files = $$PWD/config.ini
    INSTALLS += config

    # For immediate copying during build
    QMAKE_POST_LINK += $$quote(cp -r $$PWD/video $$OUT_PWD/)
    QMAKE_POST_LINK += $$quote(cp -r $$PWD/data $$OUT_PWD/)
    QMAKE_POST_LINK += $$quote(cp $$PWD/config.ini $$OUT_PWD/)
}

# Default rules for deployment.
qnx: target.path = /tmp/$${TARGET}/bin
else: unix:!android: target.path = /opt/$${TARGET}/bin
!isEmpty(target.path): INSTALLS += target
