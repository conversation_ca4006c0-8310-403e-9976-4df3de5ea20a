#include "configmanager_qml.h"
#include <QDebug>
#include "data.h"
#include "dcs.h"

// 外部全局变量声明
extern std::unordered_map<std::string, Boiler*> boiler_map;
extern std::unordered_map<std::string, DCSDevice*> dcs_map;
extern std::unordered_map<std::string, int> protocol_fd_map;
extern ConfigManager* g_config_manager;

// 外部函数声明
extern std::unordered_map<std::string, int> start_fd(ConfigManager *config_manager);

ConfigManagerQML::ConfigManagerQML(QObject *parent)
    : QObject(parent), m_configManager(nullptr)
{
}

void ConfigManagerQML::setConfigManager(ConfigManager* manager)
{
    m_configManager = manager;
}

QVariantMap ConfigManagerQML::getRS485Config()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return QVariantMap();
    }
    
    return sectionToVariantMap("RS485");
}

QVariantMap ConfigManagerQML::getDCSRS485Config()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return QVariantMap();
    }
    
    return sectionToVariantMap("DCS_RS485");
}

bool ConfigManagerQML::saveRS485Config(const QVariantMap& config)
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }
    
    bool success = variantMapToSection("RS485", config);
    if (success) {
        success = m_configManager->save();
        emit configSaved(success);
        if (success) {
            emit configChanged();
        }
    }
    return success;
}

bool ConfigManagerQML::saveDCSRS485Config(const QVariantMap& config)
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }
    
    bool success = variantMapToSection("DCS_RS485", config);
    if (success) {
        success = m_configManager->save();
        emit configSaved(success);
        if (success) {
            emit configChanged();
        }
    }
    return success;
}

bool ConfigManagerQML::saveAllConfigs()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }
    
    bool success = m_configManager->save();
    emit configSaved(success);
    if (success) {
        emit configChanged();
    }
    return success;
}

bool ConfigManagerQML::reloadConfigs()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }
    
    bool success = m_configManager->load();
    emit configReloaded(success);
    if (success) {
        emit configChanged();
    }
    return success;
}

bool ConfigManagerQML::restartDataCollection()
{
    if (!m_configManager) {
        qWarning() << "ConfigManager not set";
        return false;
    }

    try {
        qDebug() << "开始重启数据采集系统...";
        qDebug() << "注意：由于使用detach()线程模式，配置更改将在下次重启软件时生效";

        // 1. 重新加载配置文件
        bool configReloaded = m_configManager->load();
        if (!configReloaded) {
            qWarning() << "重新加载配置文件失败";
            return false;
        }
        qDebug() << "配置文件重新加载成功";

        // 2. 关闭现有串口连接
        for (auto& pair : protocol_fd_map) {
            if (pair.second >= 0) {
#ifdef _WIN32
                // Windows平台关闭句柄
                if (pair.second != -1) {
                    // 在Windows上，文件句柄是HANDLE类型
                    // 但这里存储的是int，需要转换
                    qDebug() << "关闭Windows串口连接，协议:" << QString::fromStdString(pair.first);
                }
#else
                close(pair.second);
                qDebug() << "关闭Linux串口连接，协议:" << QString::fromStdString(pair.first);
#endif
            }
        }
        protocol_fd_map.clear();

        // 3. 重新初始化串口连接
        protocol_fd_map = start_fd(m_configManager);
        qDebug() << "串口连接重新初始化完成，协议数量:" << protocol_fd_map.size();

        // 4. 更新所有锅炉的配置和文件句柄
        for (auto& pair : boiler_map) {
            if (pair.second) {
                // 重新加载锅炉配置
                pair.second->load_config();

                // 重新设置文件句柄
                std::string protocol_name = m_configManager->get<std::string>(pair.second->boiler_name, "Protocol");
                auto fd_it = protocol_fd_map.find(protocol_name);
                if (fd_it != protocol_fd_map.end()) {
                    pair.second->fd = fd_it->second;
                    qDebug() << "更新锅炉" << QString::fromStdString(pair.first)
                             << "协议:" << QString::fromStdString(protocol_name)
                             << "文件句柄:" << pair.second->fd;
                } else {
                    qWarning() << "未找到锅炉" << QString::fromStdString(pair.first)
                               << "的协议" << QString::fromStdString(protocol_name) << "对应的文件句柄";
                    pair.second->fd = -1;
                }
            }
        }

        // 5. 更新所有DCS设备的配置和文件句柄
        for (auto& pair : dcs_map) {
            if (pair.second) {
                // 重新加载DCS配置
                pair.second->load_config();

                // 重新设置文件句柄
                std::string protocol_name = m_configManager->get<std::string>(pair.second->dcs_name, "Protocol");
                auto fd_it = protocol_fd_map.find(protocol_name);
                if (fd_it != protocol_fd_map.end()) {
                    pair.second->fd = fd_it->second;
                    qDebug() << "更新DCS设备" << QString::fromStdString(pair.first)
                             << "协议:" << QString::fromStdString(protocol_name)
                             << "文件句柄:" << pair.second->fd;
                } else {
                    qWarning() << "未找到DCS设备" << QString::fromStdString(pair.first)
                               << "的协议" << QString::fromStdString(protocol_name) << "对应的文件句柄";
                    pair.second->fd = -1;
                }
            }
        }

        qDebug() << "数据采集系统配置更新完成";
        qDebug() << "注意：由于现有线程使用detach()模式，新的串口配置将在现有连接失效后自动使用";
        return true;

    } catch (const std::exception& e) {
        qWarning() << "重启数据采集系统时发生错误:" << e.what();
        return false;
    }
}

QVariantMap ConfigManagerQML::sectionToVariantMap(const std::string& sectionName)
{
    QVariantMap result;
    
    if (!m_configManager) {
        return result;
    }
    
    // 获取节中的所有键
    std::vector<std::string> keys = m_configManager->get_keys(sectionName);
    
    for (const auto& key : keys) {
        std::string value = m_configManager->get<std::string>(sectionName, key, "");
        result[QString::fromStdString(key)] = QString::fromStdString(value);
    }
    
    return result;
}

bool ConfigManagerQML::variantMapToSection(const std::string& sectionName, const QVariantMap& map)
{
    if (!m_configManager) {
        return false;
    }
    
    try {
        for (auto it = map.begin(); it != map.end(); ++it) {
            std::string key = it.key().toStdString();
            std::string value = it.value().toString().toStdString();
            m_configManager->set<std::string>(sectionName, key, value);
        }
        return true;
    } catch (const std::exception& e) {
        qWarning() << "Error saving config section:" << e.what();
        return false;
    }
}
