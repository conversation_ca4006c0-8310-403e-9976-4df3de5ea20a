#include "dcs.h"
#include "data.h"
#include "csvfile.h"
#include "boiler.h"
#include <thread>
#include <chrono>
#include <cstring>
#include <unordered_map>

#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif

// 声明在data.cpp中定义的函数
extern std::vector<std::string> stringSplit(const std::string& str, char delim);
extern unsigned short calculate_crc(unsigned char *buf, int len);

// 构造函数
DCSDevice::DCSDevice(ConfigManager *config_manager, std::string dcs_name) 
    : config_manager(config_manager), dcs_name(dcs_name), is_initialized(false), fd(-1) {
    // 加载配置
    if (load_config() == 0) {
        is_initialized = true;
        debug_printf("DCS设备 '%s' 初始化成功\n", dcs_name.c_str());
    } else {
        debug_printf("DCS设备 '%s' 初始化失败\n", dcs_name.c_str());
    }
}

// 析构函数
DCSDevice::~DCSDevice() {
    // 如果有打开的文件描述符，关闭它
    if (fd >= 0) {
        close(fd);
        fd = -1;
    }
}

// 加载配置
int DCSDevice::load_config() {
    // 读取基本配置
    desc = config_manager->get<std::string>(dcs_name, "Desc", "");
    protocol = config_manager->get<std::string>(dcs_name, "Protocol", "");
    collection_interval = config_manager->get<int>(dcs_name, "CollectionInterval", 10);

    // 读取关联的锅炉名称，如果没有配置则使用锅炉列表中的第一个
    associated_boiler = config_manager->get<std::string>(dcs_name, "AssociatedBoiler", "");
    if (associated_boiler.empty()) {
        // 如果没有配置关联锅炉，使用锅炉列表中的第一个
        std::string boiler_list = config_manager->get<std::string>("BoilerList", "list");
        if (!boiler_list.empty()) {
            size_t comma_pos = boiler_list.find(',');
            if (comma_pos != std::string::npos) {
                associated_boiler = boiler_list.substr(0, comma_pos);
            } else {
                associated_boiler = boiler_list;
            }
        }
    }
    
    // 读取Modbus配置
    device_address = config_manager->get<int>(dcs_name, "DeviceAddress", 1);
    start_register = config_manager->get<int>(dcs_name, "StartRegister", 0);
    register_count = config_manager->get<int>(dcs_name, "RegisterCount", 44);
    
    // 读取数据偏移量配置
    furnace_pressure_offset = config_manager->get<int>(dcs_name, "FurnacePressureOffset", 0);
    superheater_temp_offset = config_manager->get<int>(dcs_name, "SuperheaterTempOffset", 2);
    generator_power_offset = config_manager->get<int>(dcs_name, "GeneratorPowerOffset", 4);
    main_steam_pressure_offset = config_manager->get<int>(dcs_name, "MainSteamPressureOffset", 6);
    total_air_flow_offset = config_manager->get<int>(dcs_name, "TotalAirFlowOffset", 8);
    water_coal_ratio_offset = config_manager->get<int>(dcs_name, "WaterCoalRatioOffset", 12);
    primary_fan_a_offset = config_manager->get<int>(dcs_name, "PrimaryFanAOffset", 14);
    primary_fan_b_offset = config_manager->get<int>(dcs_name, "PrimaryFanBOffset", 16);
    fan_a_offset = config_manager->get<int>(dcs_name, "FanAOffset", 18);
    fan_b_offset = config_manager->get<int>(dcs_name, "FanBOffset", 20);
    induced_fan_a_offset = config_manager->get<int>(dcs_name, "InducedFanAOffset", 22);
    induced_fan_b_offset = config_manager->get<int>(dcs_name, "InducedFanBOffset", 24);
    co_offset = config_manager->get<int>(dcs_name, "COOffset", 26);
    o2_offset = config_manager->get<int>(dcs_name, "O2Offset", 28);
    so2_offset = config_manager->get<int>(dcs_name, "SO2Offset", 60);
    nox_offset = config_manager->get<int>(dcs_name, "NOxOffset", 64);

    // 读取烟气分析仪数据写入DCS的寄存器地址配置
    so2_write_address = config_manager->get<int>(dcs_name, "SO2WriteAddress", 15);
    nox_write_address = config_manager->get<int>(dcs_name, "NOxWriteAddress", 17);
    // 初始化数据缓冲区
    data_buffer.resize(register_count * 2);  // 每个寄存器2字节
    
    debug_printf("DCS设备 '%s' 配置 - 协议: %s, 采集间隔: %d秒\n",
           dcs_name.c_str(), protocol.c_str(), collection_interval);
    debug_printf("DCS设备 '%s' Modbus配置 - 设备地址: %d, 起始寄存器: %d, 寄存器数量: %d\n",
           dcs_name.c_str(), device_address, start_register, register_count);
    debug_printf("DCS设备 '%s' 烟气分析仪数据写入地址 - SO2: %d, NOx: %d\n",
           dcs_name.c_str(), so2_write_address, nox_write_address);

    // 验证配置值
    debug_printf("配置验证: SO2WriteAddress=%d, NOxWriteAddress=%d\n",
                so2_write_address, nox_write_address);
    
    return 0;
}

// 从缓冲区解析浮点数（IEEE 754格式）
// 根据文档示例：40 FD 69 66 = 7.9134
float DCSDevice::parse_float(const unsigned char* buffer, int offset) {
    float value;
    unsigned char bytes[4];

    // IEEE 754大端序格式：高字节在前
    // 但在小端序系统（x86）上需要调整字节序
    bytes[3] = buffer[offset];     // 最高字节
    bytes[2] = buffer[offset + 1];
    bytes[1] = buffer[offset + 2];
    bytes[0] = buffer[offset + 3]; // 最低字节

    // 将字节转换为浮点数
    memcpy(&value, bytes, 4);

    debug_printf("解析浮点数: %02X %02X %02X %02X -> %.4f\n",
               buffer[offset], buffer[offset+1], buffer[offset+2], buffer[offset+3], value);

    return value;
}

// 将浮点数转换为IEEE 754格式的4字节数组
void DCSDevice::float_to_ieee754_bytes(float value, unsigned char* bytes) {
    // 将浮点数按IEEE 754格式转换为字节数组（大端序）
    union {
        float f;
        unsigned char b[4];
    } converter;

    converter.f = value;

    // 转换为大端序（网络字节序）
    bytes[0] = converter.b[3];  // 最高字节
    bytes[1] = converter.b[2];
    bytes[2] = converter.b[1];
    bytes[3] = converter.b[0];  // 最低字节

    debug_printf("浮点数 %.4f 转换为IEEE 754: %02X %02X %02X %02X\n",
                value, bytes[0], bytes[1], bytes[2], bytes[3]);
}

// 写入单个寄存器到DCS
int DCSDevice::write_single_register(unsigned short register_address, unsigned short value) {
    if (fd < 0) {
        debug_printf("DCS串口未连接，无法写入寄存器\n");
        return -1;
    }

    // 构建Modbus写单个寄存器请求：设备地址 + 功能码06 + 寄存器地址 + 数据值 + CRC
    unsigned char request[8];
    request[0] = device_address;                    // 设备地址
    request[1] = 0x06;                             // 功能码：写单个寄存器
    request[2] = (register_address >> 8) & 0xFF;   // 寄存器地址高字节
    request[3] = register_address & 0xFF;          // 寄存器地址低字节
    request[4] = (value >> 8) & 0xFF;              // 数据值高字节
    request[5] = value & 0xFF;                     // 数据值低字节

    // 计算CRC
    unsigned short crc = calculate_crc(request, 6);
    request[6] = crc & 0xFF;        // CRC低字节
    request[7] = (crc >> 8) & 0xFF; // CRC高字节

    debug_printf("发送DCS写寄存器请求 - 地址: %d, 值: %d: ", register_address, value);
    for (int i = 0; i < 8; i++) {
        debug_printf("%02X ", request[i]);
    }
    debug_printf("\n");

#ifdef _WIN32
    // Windows下使用WriteFile
    HANDLE hSerial = (HANDLE)(intptr_t)fd;
    DWORD bytesWritten = 0;
    BOOL writeResult = WriteFile(hSerial, request, 8, &bytesWritten, NULL);

    if (!writeResult || bytesWritten != 8) {
        DWORD error = GetLastError();
        debug_printf("DCS写寄存器失败: Windows错误码=%lu\n", error);
        return -1;
    }
#else
    // Linux下使用write
    int written = write(fd, request, 8);
    if (written != 8) {
        debug_perror("发送DCS写寄存器请求失败");
        return -1;
    }
#endif

    debug_printf("DCS写寄存器请求发送成功\n");

    // 等待响应
    std::this_thread::sleep_for(std::chrono::milliseconds(100));

    // 读取响应
    unsigned char response[8];
    int read_len = 0;

#ifdef _WIN32
    DWORD bytesRead = 0;
    BOOL readResult = ReadFile(hSerial, response, sizeof(response), &bytesRead, NULL);
    read_len = (int)bytesRead;

    if (!readResult) {
        DWORD error = GetLastError();
        debug_printf("读取DCS写寄存器响应失败: Windows错误码=%lu\n", error);
        return -1;
    }
#else
    read_len = read(fd, response, sizeof(response));
#endif

    debug_printf("DCS写寄存器响应长度: %d字节\n", read_len);
    debug_printf("DCS写寄存器响应: ");
    for (int i = 0; i < read_len; i++) {
        debug_printf("%02X ", response[i]);
    }
    debug_printf("\n");

    // DCS的响应格式可能与标准Modbus不同，我们先检查是否收到了任何响应
    if (read_len > 0) {
        // 检查是否是错误响应（功能码最高位为1表示错误）
        if (read_len >= 2 && response[0] == device_address && (response[1] & 0x80)) {
            debug_printf("DCS返回错误响应: 功能码=0x%02X, 错误码=0x%02X\n",
                        response[1] & 0x7F, read_len >= 3 ? response[2] : 0);
            return -1;
        }

        // 对于DCS，只要收到响应且不是错误响应，就认为写入成功
        debug_printf("DCS写寄存器成功 - 地址: %d, 值: %d (收到%d字节响应)\n",
                    register_address, value, read_len);
        return 0;
    } else {
        debug_printf("DCS写寄存器无响应\n");
        return -1;
    }
}

// 发送IEEE 754浮点数数据到DCS（按参考例子格式）
int DCSDevice::send_float_data_to_dcs(float so2_value, float nox_value) {
    debug_printf("*** 开始发送IEEE 754浮点数数据到DCS ***\n");
    debug_printf("DCS设备: %s, fd=%d\n", dcs_name.c_str(), fd);

    if (fd < 0) {
        debug_printf("DCS串口未连接，无法发送数据，fd=%d\n", fd);
        return -1;
    }

    debug_printf("发送浮点数数据到DCS: SO2=%.4f, NOx=%.4f\n", so2_value, nox_value);
    debug_printf("写入地址: SO2地址=%d, NOx地址=%d\n", so2_write_address, nox_write_address);

    // 构建完整的数据包，按照参考例子的格式
    // 参考例子：0C 03 40 3F 80 00 00 40 00 00 00 40 40 00 00 40 80 00 00 40 A0 00 00 40 C0 00 00 ...

    // 准备发送的浮点数数据（示例数据，你可以根据需要调整）
    float test_values[] = {1.0f, 2.0f, 3.0f, 4.0f, 5.0f, 6.0f, so2_value, nox_value};
    int value_count = sizeof(test_values) / sizeof(float);

    // 构建数据包
    unsigned char data_packet[256];
    int packet_len = 0;

    data_packet[packet_len++] = device_address;  // 0x0C 设备地址
    data_packet[packet_len++] = 0x03;           // 功能码

    // 添加浮点数数据
    for (int i = 0; i < value_count; i++) {
        unsigned char float_bytes[4];
        float_to_ieee754_bytes(test_values[i], float_bytes);

        // 添加到数据包
        for (int j = 0; j < 4; j++) {
            data_packet[packet_len++] = float_bytes[j];
        }
    }

    // 添加一些填充数据（按参考例子）
    for (int i = 0; i < 10; i++) {
        data_packet[packet_len++] = 0x00;
        data_packet[packet_len++] = 0x00;
        data_packet[packet_len++] = 0x00;
        data_packet[packet_len++] = 0x01;
    }

    // 计算CRC
    unsigned short crc = calculate_crc(data_packet, packet_len);
    data_packet[packet_len++] = crc & 0xFF;        // CRC低字节
    data_packet[packet_len++] = (crc >> 8) & 0xFF; // CRC高字节

    debug_printf("发送DCS数据包，长度: %d字节\n", packet_len);
    debug_printf("发送的数据是：\n");
    for (int i = 0; i < packet_len; i++) {
        debug_printf("%02X ", data_packet[i]);
        if ((i + 1) % 16 == 0) debug_printf("\n");
    }
    debug_printf("\n");

    // 发送数据包
#ifdef _WIN32
    HANDLE hSerial = (HANDLE)(intptr_t)fd;
    DWORD bytesWritten = 0;
    BOOL writeResult = WriteFile(hSerial, data_packet, packet_len, &bytesWritten, NULL);

    if (!writeResult || bytesWritten != packet_len) {
        DWORD error = GetLastError();
        debug_printf("DCS数据包发送失败: Windows错误码=%lu\n", error);
        return -1;
    }
#else
    int written = write(fd, data_packet, packet_len);
    if (written != packet_len) {
        debug_perror("发送DCS数据包失败");
        return -1;
    }
#endif

    debug_printf("*** DCS数据包发送成功 ***\n");
    return 0;
}

// 发送烟气分析仪数据到DCS（按照固定报文格式）
int DCSDevice::send_analyzer_data_to_dcs(float so2_value, float nox_value) {
    debug_printf("*** 开始发送烟气分析仪数据到DCS ***\n");
    debug_printf("DCS设备: %s, fd=%d\n", dcs_name.c_str(), fd);
    debug_printf("发送数据: SO2=%.4f, NOx=%.4f\n", so2_value, nox_value);

    if (fd < 0) {
        debug_printf("DCS串口未连接，无法发送数据，fd=%d\n", fd);
        return -1;
    }

    // 获取当前DCS设备的实时数据（风机导叶位置等）
    float current_primary_fan_a, current_primary_fan_b, current_fan_a, current_fan_b;
    float current_induced_fan_a, current_induced_fan_b;

    // 使用互斥锁保护数据读取
    {
        std::lock_guard<std::mutex> lock(rwMutex);
        current_primary_fan_a = primary_fan_a;
        current_primary_fan_b = primary_fan_b;
        current_fan_a = fan_a;
        current_fan_b = fan_b;
        current_induced_fan_a = induced_fan_a;
        current_induced_fan_b = induced_fan_b;
    }

    debug_printf("当前DCS风机导叶位置数据: 一次分机A=%.4f, 一次分机B=%.4f, 送风机A=%.4f, 送风机B=%.4f, 引风机A=%.4f, 引风机B=%.4f\n",
                current_primary_fan_a, current_primary_fan_b, current_fan_a, current_fan_b, current_induced_fan_a, current_induced_fan_b);

    // 构建完整的报文，按照你的参考格式
    // 0C 03 40 [数据] [CRC] - 总长度应该是3+数据长度+2
    unsigned char data_packet[128];  // 足够容纳完整报文
    int packet_len = 0;

    // 报文头部
    data_packet[packet_len++] = device_address;  // 0x0C 设备地址
    data_packet[packet_len++] = 0x03;           // 功能码
    data_packet[packet_len++] = 0x40;           // 数据长度：64字节（0x40）

    // 按顺序添加6个风机导叶位置数据（每个4字节IEEE 754格式）
    float fan_positions[] = {
        current_primary_fan_a,    // 一次分机A入口导叶位置
        current_primary_fan_b,    // 一次分机B入口导叶位置
        current_fan_a,            // 送风机A入口导叶位置
        current_fan_b,            // 送风机B入口导叶位置
        current_induced_fan_a,    // 引风机A入口导叶位置
        current_induced_fan_b     // 引风机B入口导叶位置
    };

    // 添加6个风机导叶位置数据
    for (int i = 0; i < 6; i++) {
        unsigned char fan_bytes[4];
        float_to_ieee754_bytes(fan_positions[i], fan_bytes);

        // 添加到数据包
        data_packet[packet_len++] = fan_bytes[0];
        data_packet[packet_len++] = fan_bytes[1];
        data_packet[packet_len++] = fan_bytes[2];
        data_packet[packet_len++] = fan_bytes[3];

        debug_printf("风机位置%d (%.4f) -> %02X %02X %02X %02X\n",
                    i+1, fan_positions[i], fan_bytes[0], fan_bytes[1], fan_bytes[2], fan_bytes[3]);
    }

    // 注意：根据参考报文，这里不添加SO2和NOx数据
    // 参考报文只包含6个风机数据，然后是填充数据
    debug_printf("注意：按照参考报文格式，不添加SO2和NOx数据，只发送风机导叶位置数据\n");

    // 按照标准报文格式，总长度应为67字节（包括CRC）
    // 已有：3字节头部 + 24字节风机数据 = 27字节
    // 还需要：67 - 27 - 2(CRC) = 38字节填充数据
    // 标准格式：9组"00 00 00 01" + 2字节"00 00"，共38字节
    debug_printf("添加填充数据：9组 '00 00 00 01' + 2字节 '00 00'，共38字节\n");

    // 添加9组"00 00 00 01"
    for (int i = 0; i < 9; i++) {
        data_packet[packet_len++] = 0x00;
        data_packet[packet_len++] = 0x00;
        data_packet[packet_len++] = 0x00;
        data_packet[packet_len++] = 0x01;
    }

    // 添加最后2字节"00 00"
    data_packet[packet_len++] = 0x00;
    data_packet[packet_len++] = 0x00;

    debug_printf("填充完成，当前长度: %d字节（应为65字节，加CRC后67字节）\n", packet_len);

    // 计算CRC
    unsigned short crc = calculate_crc(data_packet, packet_len);
    data_packet[packet_len++] = crc & 0xFF;        // CRC低字节
    data_packet[packet_len++] = (crc >> 8) & 0xFF; // CRC高字节

    debug_printf("发送DCS完整报文，总长度: %d字节（标准长度：67字节）\n", packet_len);

    // 验证报文长度
    if (packet_len != 67) {
        debug_printf("警告: 报文长度不正确！期望67字节，实际%d字节\n", packet_len);
    }

    debug_printf("完整报文数据: ");
    for (int i = 0; i < packet_len; i++) {
        debug_printf("%02X ", data_packet[i]);
        // 每20字节换行，便于查看
        if ((i + 1) % 20 == 0) debug_printf("\n                ");
    }
    debug_printf("\n");

    // 验证报文结构
    debug_printf("报文结构验证:\n");
    debug_printf("  头部: %02X %02X %02X (设备地址+功能码+数据长度标识)\n", data_packet[0], data_packet[1], data_packet[2]);
    debug_printf("  风机数据: 6个浮点数，共24字节\n");
    debug_printf("  填充数据: 9组'00 00 00 01' + 2字节'00 00'，共38字节\n");
    debug_printf("  CRC校验: %02X %02X\n", data_packet[packet_len-2], data_packet[packet_len-1]);

    // 发送数据包
#ifdef _WIN32
    HANDLE hSerial = (HANDLE)(intptr_t)fd;
    DWORD bytesWritten = 0;
    BOOL writeResult = WriteFile(hSerial, data_packet, packet_len, &bytesWritten, NULL);

    if (!writeResult || bytesWritten != packet_len) {
        DWORD error = GetLastError();
        debug_printf("DCS数据包发送失败: Windows错误码=%lu\n", error);
        return -1;
    }
#else
    int written = write(fd, data_packet, packet_len);
    if (written != packet_len) {
        debug_perror("发送DCS数据包失败");
        return -1;
    }
#endif

    debug_printf("*** DCS完整报文发送成功 ***\n");
    return 0;
}

// 启动采集线程
void DCSDevice::start_data_collect() {
    // 创建并启动采集线程
    std::thread t(&DCSDevice::do_data_collect, this);
    t.detach();  // 分离线程，让它在后台运行
}

// 线程函数
void DCSDevice::do_data_collect() {
    // 使用已经设置好的文件描述符
    int fd = this->fd;

    if (fd < 0) {
        debug_printf("DCS设备 %s 无法获取有效的文件描述符，fd=%d\n", this->dcs_name.c_str(), fd);
        debug_printf("DCS设备未连接，但仍会创建CSV文件记录仪器分析仪数据\n");
        // 不直接返回，继续执行以创建CSV文件
    }

    debug_printf("DCS设备 %s 数据采集线程启动，文件描述符: %d\n", this->dcs_name.c_str(), fd);
    debug_printf("DCS设备配置: 地址=%d, 起始寄存器=%d, 寄存器数量=%d\n",
                device_address, start_register, register_count);

    // 使用与锅炉相同的CSV文件，以锅炉名称+日期命名
    // 初始化CSV管理器
    csv_manager_t csv;

    // 确保关联的锅炉名称不为空
    if (this->associated_boiler.empty()) {
        debug_printf("错误: DCS设备 '%s' 没有关联的锅炉名称，无法创建CSV文件\n", this->dcs_name.c_str());
        return;
    }

    debug_printf("DCS设备 '%s' 关联的锅炉: '%s'\n", this->dcs_name.c_str(), this->associated_boiler.c_str());

    // 使用锅炉名称作为CSV文件前缀
    init_csv_manager(&csv, 0, 4096, 5, this->associated_boiler.c_str());  // 4KB缓冲区，5秒刷新间隔
    
    // 持续采集数据
    int count = 0;
    int success_count = 0;

    while (true) {
        count++;  // 将计数器移到循环开始处，逻辑更清晰

        // 采集数据 - DCS请求-响应模式
        int result = this->read_data();

        // 无论DCS是否有数据，都要获取仪器分析仪数据并写入CSV
        bool has_dcs_data = (result > 0);

        // DCS数据变量
        float furnace_pressure = 0.0f, superheater_temp = 0.0f, generator_power = 0.0f, main_steam_pressure = 0.0f;
        float total_air_flow = 0.0f, water_coal_ratio = 0.0f, primary_fan_a = 0.0f, primary_fan_b = 0.0f;
        float fan_a = 0.0f, fan_b = 0.0f, induced_fan_a = 0.0f, induced_fan_b = 0.0f, co = 0.0f, o2 = 0.0f;

        if (has_dcs_data) {
            success_count++;
            // 使用互斥锁保护数据读取

            {
                std::lock_guard<std::mutex> lock(this->rwMutex);
                furnace_pressure = this->furnace_pressure;
                superheater_temp = this->superheater_temp;
                generator_power = this->generator_power;
                main_steam_pressure = this->main_steam_pressure;
                total_air_flow = this->total_air_flow;
                water_coal_ratio = this->water_coal_ratio;
                primary_fan_a = this->primary_fan_a;
                primary_fan_b = this->primary_fan_b;
                fan_a = this->fan_a;
                fan_b = this->fan_b;
                induced_fan_a = this->induced_fan_a;
                induced_fan_b = this->induced_fan_b;
                co = this->co;
                o2 = this->o2;
            }

        }

        // 获取关联锅炉的烟气分析仪数据（无论DCS是否有数据都要获取）
        float boiler_co = 0.0f, boiler_o2 = 0.0f, boiler_nox = 0.0f, boiler_so2 = 0.0f;
        float boiler_current = 0.0f, boiler_voltage = 0.0f, boiler_temperature = 0.0f;

        // 从全局锅炉映射中获取烟气分析仪数据
        extern std::unordered_map<std::string, Boiler*> boiler_map;
        auto boiler_it = boiler_map.find(this->associated_boiler);
        if (boiler_it != boiler_map.end() && boiler_it->second) {
            Boiler* boiler = boiler_it->second;
            // 使用锁保护数据读取
            std::lock_guard<std::mutex> boiler_lock(boiler->rwMutex);
            boiler_co = boiler->co;
            boiler_o2 = boiler->o2;
            boiler_nox = boiler->nox;
            boiler_so2 = boiler->so2;
            boiler_current = boiler->current;
            boiler_voltage = boiler->voltage;
            boiler_temperature = boiler->temperature;

            // 简化调试输出，避免日志过多
            if (count % 20 == 0) {  // 每20次循环输出一次
                debug_printf("DCS获取到烟气分析仪数据: CO=%.2f, O2=%.2f, NOx=%.2f, SO2=%.2f\n",
                            boiler_co, boiler_o2, boiler_nox, boiler_so2);
            }
        } else {
            if (count % 20 == 0) {  // 每20次循环输出一次
                debug_printf("DCS无法获取烟气分析仪数据: 关联锅炉='%s', 锅炉映射大小=%zu\n",
                            this->associated_boiler.c_str(), boiler_map.size());
            }
        }

        // 注释：烟气分析仪数据发送逻辑已移至boiler.cpp中，在数据采集时直接发送

        // 检查是否有任何有效数据（仪器分析仪或DCS）
        bool has_any_data = (boiler_co > 0 || boiler_o2 > 0 || boiler_nox > 0 || boiler_so2 > 0 ||
                            boiler_temperature > 0 || boiler_voltage > 0 || boiler_current > 0 ||
                            has_dcs_data);

        // 如果有任何数据，就写入CSV文件
        if (has_any_data) {
            // 格式化合并数据行 - 包含烟气分析仪和DCS所有参数
            char combined_data_line[1024];
            snprintf(combined_data_line, sizeof(combined_data_line),
                    "%.2f,%.2f,%.2f,%.2f,%.2f,%.2f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f,%.4f",
                    boiler_o2, boiler_co, boiler_nox, boiler_so2, boiler_temperature, boiler_voltage, boiler_current,  // 烟气分析仪数据
                    furnace_pressure, generator_power, main_steam_pressure, total_air_flow, water_coal_ratio,  // DCS数据
                    primary_fan_a, primary_fan_b, fan_a, fan_b, induced_fan_a, induced_fan_b);

            // 将合并数据写入CSV缓冲区
            write_to_csv_buffer(&csv, (const unsigned char*)combined_data_line, strlen(combined_data_line));

            if (has_dcs_data) {
                debug_printf("DCS+仪器分析仪数据已写入CSV: DCS数据=有效, 仪器O2=%.2f, CO=%.2f\n", boiler_o2, boiler_co);
            } else {
                debug_printf("仅仪器分析仪数据已写入CSV: O2=%.2f, CO=%.2f, NOx=%.2f, SO2=%.2f\n",
                            boiler_o2, boiler_co, boiler_nox, boiler_so2);
            }
        }

        if (count % 10 == 0) {  // 每10次请求打印一次状态
            debug_printf("DCS设备 %s 请求次数: %d, 成功响应: %d 次, 烟气分析仪数据: SO2=%.2f, NOx=%.2f\n",
                        this->dcs_name.c_str(), count, success_count, boiler_so2, boiler_nox);
        }

        // 按照配置的采集间隔进行请求
        std::this_thread::sleep_for(std::chrono::seconds(this->collection_interval));
    }

    // 关闭CSV管理器
    close_csv_manager(&csv);
    debug_printf("DCS设备 %s 数据采集线程退出\n", this->dcs_name.c_str());
}

// 采集数据 - DCS请求-响应模式
int DCSDevice::read_data() {
    if (fd < 0) {
        // 串口未连接，使用锁保护地设置无效数据
        {
            std::lock_guard<std::mutex> lock(rwMutex);
            furnace_pressure = superheater_temp = generator_power = main_steam_pressure = 0.0f;
            total_air_flow = water_coal_ratio = primary_fan_a = primary_fan_b = 0.0f;
            fan_a = fan_b = induced_fan_a = induced_fan_b = co = o2 = 0.0f;
        }
        debug_printf("DCS串口未连接，fd=%d，无法采集数据\n", fd);
        return 0;
    }

    // 首次检查串口连接状态
    static bool first_check = true;
    if (first_check) {
        first_check = false;
        debug_printf("DCS首次数据采集，检查串口连接状态...\n");
        debug_printf("协议: %s, 设备地址: %d, 文件描述符: %d\n",
                   protocol.c_str(), device_address, fd);
    }

    // 发送读取请求：0C 03 00 1F 00 2C + CRC
    unsigned char request[8];
    request[0] = device_address;  // 设备地址 0x0C
    request[1] = 0x03;           // 功能码：读取保持寄存器
    request[2] = (start_register >> 8) & 0xFF;  // 起始地址高字节
    request[3] = start_register & 0xFF;         // 起始地址低字节
    request[4] = (register_count >> 8) & 0xFF;  // 寄存器数量高字节
    request[5] = register_count & 0xFF;         // 寄存器数量低字节

    // 计算CRC
    unsigned short crc = calculate_crc(request, 6);
    request[6] = crc & 0xFF;        // CRC低字节
    request[7] = (crc >> 8) & 0xFF; // CRC高字节

    // 发送请求
    debug_printf("发送DCS读取请求: ");
    for (int i = 0; i < 8; i++) {
        debug_printf("%02X ", request[i]);
    }
    debug_printf("\n");

    // 检查文件描述符有效性
    debug_printf("DCS设备文件描述符: %d\n", fd);

#ifdef _WIN32
    // Windows下检查HANDLE有效性
    HANDLE hSerial = (HANDLE)(intptr_t)fd;
    debug_printf("Windows HANDLE: %p\n", hSerial);

    // 检查串口状态
    COMSTAT comStat;
    DWORD errors;
    if (ClearCommError(hSerial, &errors, &comStat)) {
        debug_printf("串口状态检查成功 - 输入队列: %lu字节, 输出队列: %lu字节\n",
                   comStat.cbInQue, comStat.cbOutQue);
        if (errors != 0) {
            debug_printf("串口错误标志: 0x%08lX\n", errors);
        }
    } else {
        DWORD error = GetLastError();
        debug_printf("无法获取串口状态，错误码: %lu\n", error);
        debug_printf("串口可能已断开或无效\n");
        return 0;
    }

    // 尝试刷新串口缓冲区
    if (!FlushFileBuffers(hSerial)) {
        DWORD error = GetLastError();
        debug_printf("刷新串口缓冲区失败，错误码: %lu\n", error);
    }
#endif

    // 直接使用Windows API进行写入，绕过可能有问题的write()包装
#ifdef _WIN32
    DWORD bytesWritten = 0;
    BOOL writeResult = WriteFile(hSerial, request, 8, &bytesWritten, NULL);
    debug_printf("WriteFile结果: %s, 写入字节数: %lu, 期望: 8\n",
               writeResult ? "成功" : "失败", bytesWritten);

    if (!writeResult || bytesWritten != 8) {
        DWORD error = GetLastError();
        debug_printf("WriteFile失败: Windows错误码=%lu, HANDLE=%p\n", error, hSerial);

        // 再次检查串口状态
        if (ClearCommError(hSerial, &errors, &comStat)) {
            debug_printf("写入失败后串口错误状态: 0x%08lX\n", errors);
        }

        // 尝试重新打开串口
        debug_printf("尝试重新检查串口连接...\n");
        return 0;
    }

    debug_printf("DCS请求发送成功，使用WriteFile直接写入\n");
#else
    int written = write(fd, request, 8);
    debug_printf("write()返回值: %d, 期望: 8\n", written);

    if (written != 8) {
        if (written < 0) {
            debug_perror("发送DCS请求失败");
        } else {
            debug_printf("发送DCS请求不完整: 发送了%d字节，期望8字节\n", written);
        }
        return 0;
    }

    debug_printf("DCS请求发送成功\n");
#endif

    debug_printf("DCS请求发送成功\n");

    // 等待响应
    std::this_thread::sleep_for(std::chrono::milliseconds(200));

    // 读取响应数据
    unsigned char buffer[512];
    int read_len = 0;

#ifdef _WIN32
    // 使用Windows API直接读取
    DWORD bytesRead = 0;
    BOOL readResult = ReadFile(hSerial, buffer, sizeof(buffer), &bytesRead, NULL);
    read_len = (int)bytesRead;

    debug_printf("ReadFile结果: %s, 读取字节数: %lu\n",
               readResult ? "成功" : "失败", bytesRead);

    if (!readResult) {
        DWORD error = GetLastError();
        debug_printf("ReadFile失败: Windows错误码=%lu\n", error);
        return 0;
    }
#else
    read_len = read(fd, buffer, sizeof(buffer));
#endif

    if (read_len <= 0) {
        debug_printf("未收到DCS响应数据，读取长度: %d\n", read_len);
        return 0;
    }

    debug_printf("接收到DCS响应，长度: %d字节\n", read_len);
    debug_printf("DCS响应数据: ");
    for (int i = 0; i < read_len && i < 50; i++) {
        debug_printf("%02X ", buffer[i]);
    }
    debug_printf("\n");

    // 查找DCS响应报文头：0C 10 00 1F 00 2C 58
    bool found_dcs_frame = false;
    int frame_start = -1;

    for (int i = 0; i <= read_len - 97; i++) {
        if (buffer[i] == 0x0C && buffer[i+1] == 0x10 &&
            buffer[i+2] == 0x00 && buffer[i+3] == 0x1F &&
            buffer[i+4] == 0x00 && buffer[i+5] == 0x2C &&
            buffer[i+6] == 0x58) {
            found_dcs_frame = true;
            frame_start = i;
            debug_printf("找到DCS响应报文，起始位置: %d\n", frame_start);
            break;
        }
    }

    if (!found_dcs_frame) {
        debug_printf("未找到有效的DCS响应报文头\n");
        return 0;
    }

    // 验证报文长度
    if (read_len < frame_start + 97) {
        debug_printf("DCS响应报文长度不足，需要97字节，实际: %d字节\n", read_len - frame_start);
        return 0;
    }

    // 解析88字节的IEEE 754数据（跳过7字节头部）
    unsigned char* data_start = &buffer[frame_start + 7];

    // 根据配置的字节偏移量解析各个参数
    float temp_furnace_pressure = parse_float(data_start, furnace_pressure_offset);
    float temp_superheater_temp = parse_float(data_start, superheater_temp_offset);
    float temp_generator_power = parse_float(data_start, generator_power_offset);
    float temp_main_steam_pressure = parse_float(data_start, main_steam_pressure_offset);
    float temp_total_air_flow = parse_float(data_start, total_air_flow_offset);
    float temp_water_coal_ratio = parse_float(data_start, water_coal_ratio_offset);
    float temp_primary_fan_a = parse_float(data_start, primary_fan_a_offset);
    float temp_primary_fan_b = parse_float(data_start, primary_fan_b_offset);
    float temp_fan_a = parse_float(data_start, fan_a_offset);
    float temp_fan_b = parse_float(data_start, fan_b_offset);
    float temp_induced_fan_a = parse_float(data_start, induced_fan_a_offset);
    float temp_induced_fan_b = parse_float(data_start, induced_fan_b_offset);
    float temp_co = parse_float(data_start, co_offset);
    float temp_o2 = parse_float(data_start, o2_offset);

    // 更新成员变量（使用互斥锁保证线程安全）
    {
        std::lock_guard<std::mutex> lock(rwMutex);
        furnace_pressure = temp_furnace_pressure;
        superheater_temp = temp_superheater_temp;
        generator_power = temp_generator_power;
        main_steam_pressure = temp_main_steam_pressure;
        total_air_flow = temp_total_air_flow;
        water_coal_ratio = temp_water_coal_ratio;
        primary_fan_a = temp_primary_fan_a;
        primary_fan_b = temp_primary_fan_b;
        fan_a = temp_fan_a;
        fan_b = temp_fan_b;
        induced_fan_a = temp_induced_fan_a;
        induced_fan_b = temp_induced_fan_b;
        co = temp_co;
        o2 = temp_o2;
    }

    debug_printf("DCS数据解析成功: 炉膛压力=%.4f, 过热器温度=%.4f, 发电机功率=%.4f, 主蒸汽压力=%.4f,一次风机A入口导叶位置%.4f,一次风机B入口导叶位置%.4f,送风机A入口导叶位置%.4f,送风机B入口导叶位置%.4f,引风机A入口导叶位置%.4f,引风机B入口导叶位置%.4f\n",
               temp_furnace_pressure, temp_superheater_temp, temp_generator_power, temp_main_steam_pressure,temp_primary_fan_a,temp_primary_fan_b,temp_fan_a,temp_fan_b,temp_induced_fan_a,temp_induced_fan_b);
    debug_printf("DCS其他数据: 总风量=%.4f, 水煤比=%.4f, CO=%.4f, O2=%.4f\n",
               temp_total_air_flow, temp_water_coal_ratio, temp_co, temp_o2);

    return 1;  // 返回1表示成功解析了一帧数据
}

// 全局DCS设备映射
std::unordered_map<std::string, DCSDevice*> dcs_map;

// 获取DCS设备列表
std::unordered_map<std::string, DCSDevice*> get_dcs_list(ConfigManager *config_manager) {
    // 读取DCS设备列表配置
    std::string dcs_list = config_manager->get<std::string>("DCSList", "list");
    debug_printf("调试: 从配置读取的DCS设备列表: '%s'\n", dcs_list.c_str());

    std::unordered_map<std::string, DCSDevice*> dcs_device_map;
    std::vector<std::string> vect_dcs = stringSplit(dcs_list, ',');

    debug_printf("调试: 解析后的DCS设备数量: %zu\n", vect_dcs.size());

    for (const auto& dcs_name : vect_dcs) {
        debug_printf("调试: 创建DCS设备: '%s'\n", dcs_name.c_str());
        DCSDevice* dcs_device = new DCSDevice(config_manager, dcs_name);

        // 检查DCS设备是否成功初始化
        if (dcs_device->is_initialized) {
            dcs_device_map[dcs_name] = dcs_device;
            debug_printf("调试: DCS设备 '%s' 创建并初始化成功\n", dcs_name.c_str());
        } else {
            debug_printf("错误: DCS设备 '%s' 初始化失败，跳过该设备\n", dcs_name.c_str());
            delete dcs_device;  // 释放内存
        }
    }

    debug_printf("调试: 总共创建了 %zu 个DCS设备\n", dcs_device_map.size());
    return dcs_device_map;
}

// 获取实时DCS数据
void get_realtime_dcs_data(std::string dcs_name,
                          float *furnace_pressure,
                          float *superheater_temp,
                          float *generator_power,
                          float *main_steam_pressure,
                          float *total_air_flow,
                          float *water_coal_ratio,
                          float *co,
                          float *o2,
                          float *primary_fan_a,
                          float *primary_fan_b,
                          float *fan_a,
                          float *fan_b,
                          float *induced_fan_a,
                          float *induced_fan_b) {
    // 调试信息：打印DCS设备映射状态
    debug_printf("调试: 请求DCS设备名称: %s\n", dcs_name.c_str());
    debug_printf("调试: dcs_map大小: %zu\n", dcs_map.size());

    if (dcs_map.empty()) {
        debug_printf("错误: dcs_map为空！\n");
        return;
    }

    // 查找指定的DCS设备
    auto it = dcs_map.find(dcs_name);
    if (it == dcs_map.end()) {
        debug_printf("错误: 找不到DCS设备 '%s'\n", dcs_name.c_str());
        // 设置默认值
        *furnace_pressure = *superheater_temp = *generator_power = *main_steam_pressure = 0.0f;
        *total_air_flow = *water_coal_ratio = *co = *o2 = 0.0f;
        *primary_fan_a = *primary_fan_b = *fan_a = *fan_b = 0.0f;
        *induced_fan_a = *induced_fan_b = 0.0f;
        return;
    }

    DCSDevice* dcs_device = it->second;
    if (!dcs_device) {
        debug_printf("错误: DCS设备 '%s' 对象为空\n", dcs_name.c_str());
        *furnace_pressure = *superheater_temp = *generator_power = *main_steam_pressure = 0.0f;
        *total_air_flow = *water_coal_ratio = *co = *o2 = 0.0f;
        *primary_fan_a = *primary_fan_b = *fan_a = *fan_b = 0.0f;
        *induced_fan_a = *induced_fan_b = 0.0f;
        return;
    }

    // 使用互斥锁保护数据读取
    {
        std::lock_guard<std::mutex> lock(dcs_device->rwMutex);
        *furnace_pressure = dcs_device->furnace_pressure;
        *superheater_temp = dcs_device->superheater_temp;
        *generator_power = dcs_device->generator_power;
        *main_steam_pressure = dcs_device->main_steam_pressure;
        *total_air_flow = dcs_device->total_air_flow;
        *water_coal_ratio = dcs_device->water_coal_ratio;
        *co = dcs_device->co;
        *o2 = dcs_device->o2;
        *primary_fan_a = dcs_device->primary_fan_a;
        *primary_fan_b = dcs_device->primary_fan_b;
        *fan_a = dcs_device->fan_a;
        *fan_b = dcs_device->fan_b;
        *induced_fan_a = dcs_device->induced_fan_a;
        *induced_fan_b = dcs_device->induced_fan_b;
    }

    debug_printf("调试: DCS设备 '%s' 数据获取成功 - 炉膛压力=%.4f, 过热器温度=%.4f, 发电机功率=%.4f\n",
                dcs_name.c_str(), *furnace_pressure, *superheater_temp, *generator_power);
}