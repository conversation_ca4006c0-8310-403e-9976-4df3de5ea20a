#include "csvfile.h"
#include "data.h"
#include <errno.h>
#include <time.h>
#include <stdio.h>
#ifdef _WIN32
    #include <direct.h>
    #include <windows.h>
    #define mkdir(path, mode) _mkdir(path)
#else
    #include <sys/stat.h>
    #include <sys/types.h>
    #include <unistd.h>
#endif

// 获取可执行文件所在目录
static void get_executable_dir(char *dir_path, size_t size) {
#ifdef _WIN32
    char exe_path[MAX_PATH];
    DWORD len = GetModuleFileNameA(NULL, exe_path, MAX_PATH);
    if (len > 0) {
        // 找到最后一个反斜杠的位置
        char *last_slash = strrchr(exe_path, '\\');
        if (last_slash) {
            *last_slash = '\0';  // 截断到目录部分
            strncpy(dir_path, exe_path, size - 1);
            dir_path[size - 1] = '\0';
        } else {
            strcpy(dir_path, ".");
        }
    } else {
        strcpy(dir_path, ".");
    }
#else
    char exe_path[1024];
    ssize_t len = readlink("/proc/self/exe", exe_path, sizeof(exe_path) - 1);
    if (len != -1) {
        exe_path[len] = '\0';
        char *last_slash = strrchr(exe_path, '/');
        if (last_slash) {
            *last_slash = '\0';
            strncpy(dir_path, exe_path, size - 1);
            dir_path[size - 1] = '\0';
        } else {
            strcpy(dir_path, ".");
        }
    } else {
        strcpy(dir_path, ".");
    }
#endif
}

// 初始化CSV管理器
int init_csv_manager(csv_manager_t *csv, size_t max_size, size_t flush_threshold, int flush_interval, const char *fileprefix) {
    memset(csv, 0, sizeof(csv_manager_t));
    csv->max_size = max_size;
    csv->flush_threshold = flush_threshold;
    csv->flush_interval = flush_interval;
    csv->last_flush_time = time(NULL);
    strcpy(csv->fileprefix,fileprefix);

    // 获取可执行文件所在目录
    char exe_dir[512];
    get_executable_dir(exe_dir, sizeof(exe_dir));

    // 构建data目录的完整路径
    char data_dir[600];
    snprintf(data_dir, sizeof(data_dir), "%s%sdata", exe_dir,
#ifdef _WIN32
        "\\"
#else
        "/"
#endif
    );

    // 创建数据目录（如果不存在）
    debug_printf("尝试创建数据目录: %s\n", data_dir);
    int result = mkdir(data_dir, 0755);
    if (result == 0) {
        debug_printf("数据目录创建成功: %s\n", data_dir);
    } else if (errno == EEXIST) {
        debug_printf("数据目录已存在: %s\n", data_dir);
    } else {
        debug_printf("数据目录创建失败: %s, 错误: %s\n", data_dir, strerror(errno));
        // 尝试使用当前目录下的data文件夹
        result = mkdir("data", 0755);
        if (result != 0 && errno != EEXIST) {
            debug_printf("当前目录下创建data文件夹也失败: %s\n", strerror(errno));
        }
    }

    return 0;
}

// 创建新的CSV文件
int create_new_csv_file(csv_manager_t *csv) {
    // 关闭已打开的文件
    if(csv->file) {
        fflush(csv->file);
        fclose(csv->file);
        csv->file = NULL;
    }

    // 获取当前时间戳
    time_t now = time(NULL);
    csv->start_time = now;

    // 格式化文件名（时间戳秒数）
    struct tm *timeinfo = localtime(&now);
    char today[60] = {0};
    strftime(today, sizeof(today), "%Y%m%d.csv", timeinfo);

    // 获取可执行文件所在目录
    char exe_dir[512];
    get_executable_dir(exe_dir, sizeof(exe_dir));

    // 检查fileprefix是否包含中文字符，如果包含则使用英文替代
    char safe_prefix[128];
    bool has_chinese = false;
    for (int i = 0; csv->fileprefix[i] != '\0'; i++) {
        if ((unsigned char)csv->fileprefix[i] > 127) {
            has_chinese = true;
            break;
        }
    }

    if (has_chinese) {
        // 如果包含中文，使用锅炉名称的安全版本+日期
        strftime(safe_prefix, sizeof(safe_prefix), "boiler_%Y%m%d", timeinfo);
        snprintf(csv->filename, sizeof(csv->filename), "%s%sdata%s%s.csv", exe_dir,
#ifdef _WIN32
            "\\", "\\"
#else
            "/", "/"
#endif
            , safe_prefix);
    } else {
        // 如果不包含中文，使用原来的逻辑
        snprintf(csv->filename, sizeof(csv->filename), "%s%sdata%s%s-%s", exe_dir,
#ifdef _WIN32
            "\\", "\\"
#else
            "/", "/"
#endif
            , csv->fileprefix, today);
    }

    int exists = access(csv->filename, F_OK);

    //如果启用文件最大小机制，则需要更换文件名
    if (csv->max_size>0 && exists != -1) {
        if (has_chinese) {
            // 使用日期+序号的方式，而不是时间戳
            static int file_counter = 1;
            strftime(safe_prefix, sizeof(safe_prefix), "boiler_%Y%m%d", timeinfo);
            snprintf(csv->filename, sizeof(csv->filename), "%s%sdata%s%s_%d.csv", exe_dir,
#ifdef _WIN32
                "\\", "\\"
#else
                "/", "/"
#endif
                , safe_prefix, file_counter++);
        } else {
            strftime(today, sizeof(today), "%Y%m%d-%H%m%s.csv", timeinfo);
            snprintf(csv->filename, sizeof(csv->filename), "%s%sdata%s%s-%s", exe_dir,
#ifdef _WIN32
                "\\", "\\"
#else
                "/", "/"
#endif
                , csv->fileprefix, today);
        }
    }
    
    // 创建新文件并写入UTF-8 BOM
    csv->file = fopen(csv->filename, "a+");
    if(!csv->file) {
        debug_perror("创建CSV文件失败");
        return -1;
    }
    
    // 写入UTF-8 BOM标记
    // unsigned char bom[3] = {0xEF, 0xBB, 0xBF};
    // fwrite(bom, 1, 3, csv->file);
    
    // 写入扩展的CSV表头，包含烟气分析仪和DCS数据
    if (exists == -1) {
        fprintf(csv->file, "时间戳,O2,CO,NOx,SO2,测点温度,电压,电流,炉膛压力,发电机功率,主蒸汽压力,总风量,水煤比,一次风机A,一次风机B,送风机A,送风机B,引风机A,引风机B\n");
    }
    
    csv->current_size = ftell(csv->file);
    csv->buffer_size = 0;
    
    debug_printf("创建新CSV文件: %s\n", csv->filename);
    return 0;
}

// 检查是否需要创建新的日期文件
int need_new_date_file(csv_manager_t *csv) {
    time_t now = time(NULL);
    struct tm *current_tm = localtime(&now);
    struct tm *start_tm = localtime(&csv->start_time);

    // 检查年、月、日是否相同
    if (current_tm->tm_year != start_tm->tm_year ||
        current_tm->tm_mon != start_tm->tm_mon ||
        current_tm->tm_mday != start_tm->tm_mday) {
        debug_printf("检测到日期变化，需要创建新的CSV文件\n");
        debug_printf("文件创建日期: %04d-%02d-%02d, 当前日期: %04d-%02d-%02d\n",
                   start_tm->tm_year + 1900, start_tm->tm_mon + 1, start_tm->tm_mday,
                   current_tm->tm_year + 1900, current_tm->tm_mon + 1, current_tm->tm_mday);
        return 1;  // 需要新文件
    }
    return 0;  // 不需要新文件
}

// 将数据写入CSV缓冲区
int write_to_csv_buffer(csv_manager_t *csv, const unsigned char *data, size_t length) {
    // 检查是否需要创建新文件（日期变化、文件大小超限或首次创建）
    if(!csv->file ||
       need_new_date_file(csv) ||
       (csv->max_size>0 && csv->current_size >= csv->max_size)) {
        if(create_new_csv_file(csv) != 0) {
            return -1;
        }
    }
    
    // 获取当前时间戳
    time_t now = time(NULL);
    
    // 格式化数据行
    char line[2048];
    int line_length = snprintf(line, sizeof(line), "%ld,%s\n", now,data);
    
    // 检查缓冲区是否有足够空间
    if(csv->buffer_size + line_length >= sizeof(csv->buffer)) {
        debug_printf("buffer sufficent refresh!\n");
        // 缓冲区已满，刷新到文件
        if(flush_csv_buffer(csv) != 0) {
            return -1;
        }
    }
    
    // 将时间戳和数据长度添加到缓冲区
    memcpy(csv->buffer + csv->buffer_size, line, line_length);
    csv->buffer_size += line_length;
    
    // 检查是否需要刷新缓冲区
    time_t current_time = time(NULL);
    if(csv->buffer_size >= csv->flush_threshold || 
       current_time - csv->last_flush_time >= csv->flush_interval) {
        debug_printf("timeover refresh!\n");
        if(flush_csv_buffer(csv) != 0) {
            return -1;
        }
    }
    
    return 0;
}

// 刷新CSV缓冲区到文件
int flush_csv_buffer(csv_manager_t *csv) {
    if(!csv->file || csv->buffer_size == 0) {
        return 0;
    }
    
    size_t written = fwrite(csv->buffer, 1, csv->buffer_size, csv->file);
    if(written != csv->buffer_size) {
        debug_perror("写入CSV文件失败");
        return -1;
    }
    
    csv->current_size += csv->buffer_size;
    csv->buffer_size = 0;
    csv->last_flush_time = time(NULL);
    
    return 0;
}

// 关闭CSV文件
void close_csv_manager(csv_manager_t *csv) {
    if(csv->file) {
        // 确保所有数据都写入文件
        if(csv->buffer_size > 0) {
            flush_csv_buffer(csv);
        }
        
        fclose(csv->file);
        csv->file = NULL;
        debug_printf("CSV文件已关闭: %s\n", csv->filename);
    }
}
